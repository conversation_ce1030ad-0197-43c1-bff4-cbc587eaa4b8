<style type="text/tailwindcss">
  @layer components {
    .badge-new {
      @apply inline-block -rotate-6 -translate-y-1 mx-1 px-1 bg-[#FE4A60] border border-gray-900 text-white text-[10px] font-bold shadow-[2px_2px_0_0_rgba(0,0,0,1)];
    }
    .landing-page-title {
      @apply inline-block w-full relative text-center text-4xl sm:text-5xl md:text-6xl lg:text-7xl sm:pt-20 lg:pt-5 font-bold tracking-tighter;
    }
    .intro-text {
      @apply text-center text-gray-600 text-lg max-w-2xl mx-auto;
    }
    .sparkle-red {
      @apply absolute flex-shrink-0 h-auto w-14 sm:w-20 md:w-24 p-2 left-0 lg:ml-32 -translate-x-2 md:translate-x-10 lg:-translate-x-full -translate-y-4 sm:-translate-y-8 md:-translate-y-0 lg:-translate-y-10;
    }
    .sparkle-green {
      @apply absolute flex-shrink-0 right-0 bottom-0 w-10 sm:w-16 lg:w-20 -translate-x-10 lg:-translate-x-12 translate-y-4 sm:translate-y-10 md:translate-y-2 lg:translate-y-4;
    }
    .pattern-select {
      @apply min-w-max appearance-none pr-6 pl-2 py-2 bg-[#e6e8eb] border-r-[3px] border-gray-900 cursor-pointer focus:outline-none;
    }
  }

  @layer utilities {
    .no-drag {
      @apply pointer-events-none select-none;
      -webkit-user-drag: none;
    }
    .link-bounce {
      @apply transition-transform hover:-translate-y-0.5;
    }
  }
</style>
