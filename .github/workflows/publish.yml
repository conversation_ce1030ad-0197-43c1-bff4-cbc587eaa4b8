name: Publish to PyPI

on:
  release:
    types: [created] # Run when you click “Publish release”
  workflow_dispatch: # ... or run it manually from the Actions tab

permissions:
  contents: read

jobs:
  release-build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.13
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"
          cache: pip
          cache-dependency-path: pyproject.toml

      - name: Build package
        run: |
          python -m pip install --upgrade pip
          python -m pip install build twine
          twine check dist/*
      - name: Upload dist artefact
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/

# Publish to PyPI (only if “dist/” succeeded)
  pypi-publish:
    needs: release-build
    runs-on: ubuntu-latest
    environment: pypi

    permissions:
      id-token: write # OIDC token for trusted publishing

    steps:
      - uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist/

      - uses: pypa/gh-action-pypi-publish@release/v1
        with:
          verbose: true
