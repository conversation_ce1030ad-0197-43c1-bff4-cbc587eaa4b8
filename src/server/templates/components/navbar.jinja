<header class="sticky top-0 bg-[#FFFDF8] border-b-[3px] border-gray-900 z-50">
    <div class="max-w-4xl mx-auto px-4">
        <div class="flex justify-between items-center h-16">
            {# Logo #}
            <div class="flex items-center gap-4">
                <h1 class="text-2xl font-bold tracking-tight">
                    <a href="/" class="hover:opacity-80 transition-opacity">
                        <span class="text-gray-900">Git</span><span class="text-[#FE4A60]">ingest</span>
                    </a>
                </h1>
            </div>
            {# Navigation with updated styling #}
            <nav class="flex items-center space-x-6">
                <a href="/llm.txt" class="link-bounce flex items-center text-gray-900">
                    <span class="badge-new">NEW</span>
                    /llm.txt
                </a>
                {# GitHub link #}
                <div class="flex items-center gap-2">
                    <a href="https://github.com/cyclotruc/gitingest"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="link-bounce flex items-center gap-1.5 text-gray-900">
                        <img src="/static/icons/github.svg" class="w-4 h-4" alt="GitHub logo">
                        GitHub
                    </a>
                    {# Star counter #}
                    <div class="no-drag flex items-center text-sm text-gray-600">
                        <img src="/static/svg/github-star.svg"
                             class="w-4 h-4 mr-1"
                             alt="GitHub star icon">
                        <span id="github-stars">0</span>
                    </div>
                </div>
            </nav>
        </div>
    </div>
</header>
{# Load GitHub stars script #}
<script defer src="/static/js/navbar.js"></script>
