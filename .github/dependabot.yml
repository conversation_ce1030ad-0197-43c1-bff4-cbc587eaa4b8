version: 2
updates:
  # ─── <PERSON> (pip) ─────────────────────────────
  - package-ecosystem: "pip"
    directory: "/"
    schedule: { interval: "weekly" }
    labels: [ "dependencies", "pip" ]
    groups: # Group patches & minors from dev-only tools
      dev-py:
        dependency-type: "development"
        update-types: ["minor", "patch"]

  # ─── Git<PERSON>ub <PERSON> ───────────────────────────
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule: { interval: "weekly" }
    labels: [ "dependencies", "gh-actions" ]
