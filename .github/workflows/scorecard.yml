name: OSSF Scorecard
on:
  branch_protection_rule:
  schedule:
    - cron: '33 11 * * 2'  # Every Tuesday at 11:33 AM UTC
  push:
    branches: [ main ]

permissions: read-all

concurrency: # avoid overlapping runs
  group: scorecard-${{ github.ref }}
  cancel-in-progress: true

jobs:
  analysis:
    name: Scorecard analysis
    runs-on: ubuntu-latest
    permissions:
      security-events: write # upload SARIF to code-scanning
      id-token: write # publish results for the badge

    steps:
      - name: Checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11
        with:
          persist-credentials: false

      - name: Run Scorecard
        uses: ossf/scorecard-action@05b42c624433fc40578a4040d5cf5e36ddca8cde
        with:
          results_file: results.sarif
          results_format: sarif
          publish_results: true  # enables the public badge

      - name: Upload to code-scanning
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: results.sarif
