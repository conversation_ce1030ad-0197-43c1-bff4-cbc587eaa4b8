name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

permissions:
  contents: read

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: true
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Locate pip cache
      id: pip-cache
      shell: bash
      run: echo "dir=$(python -m pip cache dir)" >> "$GITHUB_OUTPUT"

    - name: Cache pip
      uses: actions/cache@v4
      with:
        path: ${{ steps.pip-cache.outputs.dir }}
        key: ${{ runner.os }}-pip-${{ hashFiles('pyproject.toml') }}
        restore-keys: ${{ runner.os }}-pip-
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install ".[dev]"

    - name: Run tests
      run: pytest

    #  Run pre-commit only on Python 3.13 + ubuntu.
    - name: Run pre-commit hooks
      uses: pre-commit/action@v3.0.1
      if: ${{ matrix.python-version == '3.13' && matrix.os == 'ubuntu-latest' }}
